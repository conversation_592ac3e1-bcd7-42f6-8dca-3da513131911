<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cached Events Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Google Calendar Cached Events Fix Test</h1>
    
    <div class="test-section info">
        <h2>Problem Description</h2>
        <p>This test demonstrates the fix for cached Google Calendar events not appearing in the DOM due to date format inconsistencies.</p>
        <p><strong>Issue:</strong> When Google Calendar events are stored in Chrome storage, Date objects are serialized to strings. When loaded back, the calendar rendering code expects Date objects but receives strings, causing events to not display.</p>
    </div>

    <div class="test-section">
        <h2>Test Scenarios</h2>
        <button onclick="testDateSerialization()">Test Date Serialization Issue</button>
        <button onclick="testDeserialization()">Test Deserialization Fix</button>
        <button onclick="testCalendarValidation()">Test Calendar Validation</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Fix Summary</h2>
        <ul>
            <li><strong>Added deserializeEvents() method</strong> in GoogleCalendarService to convert string dates back to Date objects</li>
            <li><strong>Updated loadSettings()</strong> to automatically deserialize cached events when loading from storage</li>
            <li><strong>Enhanced loadCachedEvents()</strong> with better validation and logging</li>
            <li><strong>Improved setGoogleEvents()</strong> in calendar.js with date validation and conversion</li>
            <li><strong>Added refreshCachedEvents()</strong> method for manual cache refresh</li>
        </ul>
    </div>

    <script>
        function testDateSerialization() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>Date Serialization Test</h3>';
            
            // Simulate the original problem
            const originalEvent = {
                id: 'google_test123',
                title: 'Test Event',
                date: new Date('2024-01-15T10:00:00Z'),
                endTime: new Date('2024-01-15T11:00:00Z'),
                isFullDay: false,
                isGoogleEvent: true
            };
            
            // Simulate Chrome storage serialization
            const serialized = JSON.stringify([originalEvent]);
            const deserialized = JSON.parse(serialized);
            
            results.innerHTML += `
                <div class="error">
                    <h4>Original Problem:</h4>
                    <pre>Original event date: ${originalEvent.date} (${typeof originalEvent.date})
After storage serialization: ${deserialized[0].date} (${typeof deserialized[0].date})
Date instanceof check: ${deserialized[0].date instanceof Date}
Calendar rendering would fail: ${!(deserialized[0].date instanceof Date)}</pre>
                </div>
            `;
        }

        function testDeserialization() {
            const results = document.getElementById('test-results');
            results.innerHTML += '<h3>Deserialization Fix Test</h3>';
            
            // Simulate cached events with string dates (the problem)
            const cachedEventsWithStringDates = [
                {
                    id: 'google_test123',
                    title: 'Test Event 1',
                    date: '2024-01-15T10:00:00.000Z',
                    endTime: '2024-01-15T11:00:00.000Z',
                    isFullDay: false,
                    isGoogleEvent: true
                },
                {
                    id: 'google_test456',
                    title: 'Test All Day Event',
                    date: '2024-01-16T00:00:00.000Z',
                    endTime: null,
                    isFullDay: true,
                    isGoogleEvent: true
                }
            ];
            
            // Apply the fix (simulate deserializeEvents method)
            const fixedEvents = cachedEventsWithStringDates.map(event => {
                const fixed = { ...event };
                if (typeof event.date === 'string') {
                    fixed.date = new Date(event.date);
                }
                if (event.endTime && typeof event.endTime === 'string') {
                    fixed.endTime = new Date(event.endTime);
                }
                return fixed;
            });
            
            results.innerHTML += `
                <div class="success">
                    <h4>After Fix:</h4>
                    <pre>Event 1 - Date: ${fixedEvents[0].date} (${typeof fixedEvents[0].date})
Event 1 - Valid Date: ${fixedEvents[0].date instanceof Date && !isNaN(fixedEvents[0].date.getTime())}
Event 2 - Date: ${fixedEvents[1].date} (${typeof fixedEvents[1].date})
Event 2 - Valid Date: ${fixedEvents[1].date instanceof Date && !isNaN(fixedEvents[1].date.getTime())}
Calendar rendering will work: ✅</pre>
                </div>
            `;
        }

        function testCalendarValidation() {
            const results = document.getElementById('test-results');
            results.innerHTML += '<h3>Calendar Validation Test</h3>';
            
            // Test various edge cases
            const testEvents = [
                { title: 'Valid Event', date: new Date(), isFullDay: false },
                { title: 'String Date Event', date: '2024-01-15T10:00:00Z', isFullDay: false },
                { title: 'Invalid Date Event', date: 'invalid-date', isFullDay: false },
                { title: 'Null Date Event', date: null, isFullDay: false },
                { title: 'Missing Date Event', isFullDay: false }
            ];
            
            const validationResults = testEvents.map((event, index) => {
                let isValid = false;
                let convertedDate = null;
                let error = null;
                
                try {
                    if (typeof event.date === 'string') {
                        convertedDate = new Date(event.date);
                        isValid = !isNaN(convertedDate.getTime());
                    } else if (event.date instanceof Date) {
                        convertedDate = event.date;
                        isValid = !isNaN(convertedDate.getTime());
                    }
                } catch (e) {
                    error = e.message;
                }
                
                return {
                    title: event.title,
                    originalDate: event.date,
                    convertedDate,
                    isValid,
                    error
                };
            });
            
            let validationHtml = '<pre>';
            validationResults.forEach((result, index) => {
                const status = result.isValid ? '✅' : '❌';
                validationHtml += `${status} ${result.title}: ${result.originalDate} → ${result.isValid ? 'Valid' : 'Invalid'}\n`;
            });
            validationHtml += '</pre>';
            
            results.innerHTML += `
                <div class="info">
                    <h4>Validation Results:</h4>
                    ${validationHtml}
                </div>
            `;
        }
    </script>
</body>
</html>
