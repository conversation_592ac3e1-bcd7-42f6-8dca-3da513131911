/**
 * Debug script for Google Calendar cached events issues
 * Run this in the browser console to diagnose and fix cached event problems
 */

// Debug functions to help diagnose and fix cached events issues
window.debugCachedEvents = {
  
  /**
   * Diagnose cached events for corruption
   */
  async diagnose() {
    console.log('🔍 Starting cached events diagnosis...');
    
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not available');
      return;
    }
    
    try {
      const diagnosis = await googleCalendarService.diagnoseCachedEvents();
      
      console.log('\n📊 DIAGNOSIS SUMMARY:');
      console.log(`Total events: ${diagnosis.totalEvents}`);
      console.log(`Valid events: ${diagnosis.validEvents}`);
      console.log(`Invalid events: ${diagnosis.invalidEvents}`);
      
      if (diagnosis.invalidEvents > 0) {
        console.log('\n⚠️ ISSUES FOUND:');
        diagnosis.issues.forEach(issue => {
          console.log(`Event ${issue.index}: ${issue.title}`);
          console.log(`  Issues: ${issue.issues.join(', ')}`);
        });
        
        console.log('\n💡 RECOMMENDED ACTIONS:');
        console.log('1. Run debugCachedEvents.clearAndResync() to clear corrupted cache and re-sync');
        console.log('2. Or run debugCachedEvents.clearOnly() to just clear the cache');
      } else {
        console.log('\n✅ No issues found with cached events');
      }
      
      return diagnosis;
    } catch (error) {
      console.error('❌ Error during diagnosis:', error);
    }
  },
  
  /**
   * Clear corrupted cache and force a new sync
   */
  async clearAndResync() {
    console.log('🧹 Clearing corrupted cache and re-syncing...');
    
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not available');
      return;
    }
    
    try {
      await googleCalendarService.clearCorruptedCache(true);
      console.log('✅ Cache cleared and fresh events synced');
      
      // Force update the calendar display
      if (window.updateCalendarWithGoogleEvents) {
        setTimeout(() => {
          updateCalendarWithGoogleEvents();
          console.log('✅ Calendar display updated');
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error clearing cache and re-syncing:', error);
    }
  },
  
  /**
   * Clear cache only (no re-sync)
   */
  async clearOnly() {
    console.log('🧹 Clearing corrupted cache only...');
    
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not available');
      return;
    }
    
    try {
      await googleCalendarService.clearCorruptedCache(false);
      console.log('✅ Cache cleared (no re-sync performed)');
      
      // Force update the calendar display
      if (window.updateCalendarWithGoogleEvents) {
        setTimeout(() => {
          updateCalendarWithGoogleEvents();
          console.log('✅ Calendar display updated');
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
    }
  },
  
  /**
   * Refresh cached events (re-deserialize from storage)
   */
  async refresh() {
    console.log('🔄 Refreshing cached events...');
    
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not available');
      return;
    }
    
    try {
      const events = await googleCalendarService.refreshCachedEvents();
      console.log(`✅ Refreshed ${events.length} cached events`);
      
      // Force update the calendar display
      if (window.updateCalendarWithGoogleEvents) {
        setTimeout(() => {
          updateCalendarWithGoogleEvents();
          console.log('✅ Calendar display updated');
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error refreshing cached events:', error);
    }
  },
  
  /**
   * Show current cached events status
   */
  showStatus() {
    console.log('📊 Current cached events status:');
    
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not available');
      return;
    }
    
    const events = googleCalendarService.getEvents();
    console.log(`Total events: ${events.length}`);
    
    if (events.length > 0) {
      console.log('Sample events (first 3):');
      events.slice(0, 3).forEach((event, index) => {
        console.log(`Event ${index + 1}:`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: event.date instanceof Date && !isNaN(event.date.getTime()),
          isFullDay: event.isFullDay
        });
      });
    }
    
    const syncStatus = googleCalendarService.getSyncStatus();
    console.log('Sync status:', syncStatus);
  },
  
  /**
   * Show help information
   */
  help() {
    console.log(`
🔧 DEBUG CACHED EVENTS HELP

Available commands:
- debugCachedEvents.diagnose()      : Diagnose cached events for corruption
- debugCachedEvents.clearAndResync(): Clear corrupted cache and re-sync fresh events
- debugCachedEvents.clearOnly()     : Clear cache only (no re-sync)
- debugCachedEvents.refresh()       : Refresh cached events from storage
- debugCachedEvents.showStatus()    : Show current cached events status
- debugCachedEvents.help()          : Show this help

Common workflow:
1. Run diagnose() to identify issues
2. If issues found, run clearAndResync() to fix them
3. Use showStatus() to verify the fix

Example:
  await debugCachedEvents.diagnose();
  await debugCachedEvents.clearAndResync();
  debugCachedEvents.showStatus();
    `);
  }
};

// Auto-run diagnosis if there are issues
setTimeout(async () => {
  if (window.googleCalendarService && window.googleCalendarService.getEvents().length > 0) {
    console.log('🔍 Auto-diagnosing cached events...');
    try {
      const diagnosis = await window.debugCachedEvents.diagnose();
      if (diagnosis && diagnosis.invalidEvents > 0) {
        console.log('\n⚠️ CORRUPTED EVENTS DETECTED!');
        console.log('Run: await debugCachedEvents.clearAndResync()');
      }
    } catch (error) {
      console.log('Auto-diagnosis failed:', error);
    }
  }
}, 2000);

console.log('🔧 Debug tools loaded. Type debugCachedEvents.help() for available commands.');
