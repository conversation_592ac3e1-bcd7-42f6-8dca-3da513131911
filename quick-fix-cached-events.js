/**
 * QUICK FIX for Google Calendar cached events NaN-NaN-NaN error
 * 
 * Copy and paste this entire script into the browser console to immediately fix the issue.
 * This will clear corrupted cached events and force a fresh sync.
 */

(async function quickFixCachedEvents() {
  console.log('🚨 QUICK FIX: Starting Google Calendar cached events repair...');
  
  try {
    // Step 1: Check if Google Calendar Service is available
    if (!window.googleCalendarService) {
      console.error('❌ Google Calendar Service not found. Make sure the extension is loaded.');
      return;
    }
    
    console.log('✅ Google Calendar Service found');
    
    // Step 2: Diagnose the current state
    console.log('🔍 Diagnosing current cached events...');
    
    const currentEvents = window.googleCalendarService.getEvents();
    console.log(`📊 Current cached events: ${currentEvents.length}`);
    
    let corruptedCount = 0;
    let validCount = 0;
    
    currentEvents.forEach((event, index) => {
      const isValid = event.date instanceof Date && !isNaN(event.date.getTime());
      if (isValid) {
        validCount++;
      } else {
        corruptedCount++;
        console.log(`❌ Corrupted event ${index + 1}: ${event.title}`, {
          date: event.date,
          dateType: typeof event.date,
          isValidDate: isValid
        });
      }
    });
    
    console.log(`📊 Diagnosis: ${validCount} valid, ${corruptedCount} corrupted events`);
    
    // Step 3: Clear corrupted cache if any corruption found
    if (corruptedCount > 0 || currentEvents.length === 0) {
      console.log('🧹 Clearing corrupted cache and forcing fresh sync...');
      
      // Clear the cache and force sync
      await window.googleCalendarService.clearCorruptedCache(true);
      
      console.log('✅ Cache cleared and fresh sync initiated');
      
      // Wait a moment for sync to complete
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Step 4: Verify the fix
      const newEvents = window.googleCalendarService.getEvents();
      console.log(`📊 After fix: ${newEvents.length} events loaded`);
      
      let newCorruptedCount = 0;
      let newValidCount = 0;
      
      newEvents.forEach(event => {
        const isValid = event.date instanceof Date && !isNaN(event.date.getTime());
        if (isValid) {
          newValidCount++;
        } else {
          newCorruptedCount++;
        }
      });
      
      console.log(`📊 After fix: ${newValidCount} valid, ${newCorruptedCount} corrupted events`);
      
      if (newCorruptedCount === 0) {
        console.log('🎉 SUCCESS: All events are now valid!');
      } else {
        console.warn('⚠️ Some events are still corrupted. Manual intervention may be needed.');
      }
      
    } else {
      console.log('✅ No corrupted events found. Cache appears to be healthy.');
    }
    
    // Step 5: Force calendar refresh
    console.log('🔄 Forcing calendar refresh...');
    
    if (window.updateCalendarWithGoogleEvents) {
      window.updateCalendarWithGoogleEvents();
      console.log('✅ Calendar refresh completed');
    } else {
      console.warn('⚠️ updateCalendarWithGoogleEvents function not found');
    }
    
    // Step 6: Final verification
    setTimeout(() => {
      const domEvents = document.querySelectorAll('.google-time-event, .google-full-day-event');
      console.log(`📊 Final check: ${domEvents.length} Google events now visible in calendar`);
      
      if (domEvents.length > 0) {
        console.log('🎉 SUCCESS: Google Calendar events are now displaying in the calendar!');
        console.log('💡 The NaN-NaN-NaN error should be resolved.');
      } else {
        console.warn('⚠️ No Google events visible in calendar. Check if events exist for the current date range.');
      }
    }, 1000);
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    console.log('💡 Try running the debug script for more detailed troubleshooting.');
  }
})();

console.log(`
🔧 QUICK FIX SCRIPT LOADED

This script will:
1. Diagnose corrupted cached events
2. Clear corrupted cache if found
3. Force a fresh sync from Google Calendar
4. Refresh the calendar display
5. Verify the fix

The script is now running automatically...
`);
