// Function to update the badge with the count of active tasks
function updateBadgeCount() {
  chrome.storage.local.get(['tasks'], (result) => {
    const tasks = result.tasks || [];
    const activeTaskCount = tasks.length;

    // Set the badge text to the number of active tasks
    if (activeTaskCount > 0) {
      chrome.action.setBadgeText({ text: activeTaskCount.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#ff0000' }); // Red background

      // Note: Badge text color is automatically set to white for contrast in most Chrome versions
      // The setBadgeTextColor API might not be available in all Chrome versions
      if (chrome.action.setBadgeTextColor) {
        chrome.action.setBadgeTextColor({ color: '#ffffff' }); // White text
      }
    } else {
      chrome.action.setBadgeText({ text: '' }); // Clear the badge if no tasks
    }
  });
}

// Listen for clicks on the extension icon
chrome.action.onClicked.addListener(() => {
  // Open the task manager in a new tab
  chrome.tabs.create({ url: 'todo.html' });
});

// Listen for extension page navigation/refresh
chrome.webNavigation.onCompleted.addListener((details) => {
  // Check if this is our extension page
  if (details.url && details.url.includes('todo.html') && details.frameId === 0) {
    console.log('Extension page loaded/refreshed:', details.url);

    // Store the page load event for potential coordination with frontend sync
    chrome.storage.local.set({
      'extension_page_last_loaded': new Date().toISOString(),
      'extension_page_load_tab_id': details.tabId
    });
  }
}, {
  url: [{ urlMatches: 'chrome-extension://.*todo\\.html.*' }]
});

// Listen for changes to the storage
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local' && changes.tasks) {
    updateBadgeCount();
  }
});

// Update badge when the extension is loaded
updateBadgeCount();

// Google Calendar sync functionality
let googleCalendarSyncInterval = null;

// Setup Google Calendar periodic sync
function setupGoogleCalendarSync() {
  // Clear existing interval
  if (googleCalendarSyncInterval) {
    clearInterval(googleCalendarSyncInterval);
  }

  // Check if Google Calendar is enabled
  chrome.storage.local.get(['google_calendar_enabled', 'google_auth_enabled'], (result) => {
    if (result.google_calendar_enabled && result.google_auth_enabled) {
      // Sync every 15 minutes
      googleCalendarSyncInterval = setInterval(() => {
        syncGoogleCalendar('periodic');
      }, 15 * 60 * 1000);

      console.log('Google Calendar sync scheduled every 15 minutes');
    }
  });
}

// Sync Google Calendar events
async function syncGoogleCalendar(syncType = 'manual') {
  try {
    console.log(`Starting Google Calendar sync (${syncType})...`);

    // Check if frontend sync happened recently (within last 2 minutes)
    // to avoid duplicate syncs when page is refreshed
    if (syncType === 'periodic') {
      const result = await chrome.storage.local.get(['google_calendar_last_sync']);
      if (result.google_calendar_last_sync) {
        const lastSync = new Date(result.google_calendar_last_sync);
        const now = new Date();
        const timeDiff = now.getTime() - lastSync.getTime();
        const twoMinutesInMs = 2 * 60 * 1000;

        if (timeDiff < twoMinutesInMs) {
          console.log('Skipping periodic sync: Recent sync detected from frontend');
          return;
        }
      }
    }

    // Get auth token
    const token = await chrome.identity.getAuthToken({
      interactive: false,
      scopes: ['https://www.googleapis.com/auth/calendar.readonly']
    });

    if (!token) {
      console.log('No auth token available for Google Calendar sync');
      return;
    }

    // Get current week events
    const now = new Date();
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay() - 7);
    weekStart.setHours(0, 0, 0, 0);

    const weekEnd = new Date(now);
    weekEnd.setDate(now.getDate() - now.getDay() + 14);
    weekEnd.setHours(23, 59, 59, 999);

    const params = new URLSearchParams({
      timeMin: weekStart.toISOString(),
      timeMax: weekEnd.toISOString(),
      singleEvents: 'true',
      orderBy: 'startTime',
      maxResults: '250'
    });

    const response = await fetch(
      `https://www.googleapis.com/calendar/v3/calendars/primary/events?${params}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.ok) {
      const data = await response.json();
      const events = data.items || [];

      // Transform events to our format
      const transformedEvents = events.map(event => {
        const isAllDay = !event.start.dateTime;
        let startDate, endDate;

        if (isAllDay) {
          startDate = new Date(event.start.date + 'T00:00:00');
          endDate = event.end.date ? new Date(event.end.date + 'T00:00:00') : startDate;
        } else {
          startDate = new Date(event.start.dateTime);
          endDate = event.end.dateTime ? new Date(event.end.dateTime) : startDate;
        }

        return {
          id: `google_${event.id}`,
          title: event.summary || 'Untitled Event',
          description: event.description || '',
          date: startDate,
          endTime: isAllDay ? null : endDate,
          isFullDay: isAllDay,
          isGoogleEvent: true,
          googleEventId: event.id,
          location: event.location || '',
          attendees: event.attendees || [],
          htmlLink: event.htmlLink || '',
          status: event.status || 'confirmed',
          created: event.created ? new Date(event.created) : null,
          updated: event.updated ? new Date(event.updated) : null
        };
      });

      // Save events to storage
      await chrome.storage.local.set({
        google_calendar_events: transformedEvents,
        google_calendar_last_sync: new Date().toISOString()
      });

      console.log(`Synced ${transformedEvents.length} Google Calendar events (${syncType})`);
    } else {
      console.error(`Failed to sync Google Calendar (${syncType}):`, response.status);
    }
  } catch (error) {
    console.error(`Google Calendar sync error (${syncType}):`, error);
  }
}

// Listen for storage changes to setup/teardown sync
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.tasks) {
      updateBadgeCount();
    }

    if (changes.google_calendar_enabled || changes.google_auth_enabled) {
      setupGoogleCalendarSync();
    }
  }
});

// Setup Google Calendar sync on startup
setupGoogleCalendarSync();
