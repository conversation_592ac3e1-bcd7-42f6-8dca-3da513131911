// Custom Calendar Implementation
class SimpleCalendar {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;

    // Ensure we're using the current date with time set to midnight
    // This helps prevent timezone issues when calculating week start
    const now = new Date();
    this.currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    this.events = [];
    this.googleEvents = []; // Separate array for Google Calendar events
    this.onEventClick = options.onEventClick || function() {};

    this.init();
  }

  init() {
    // Create calendar structure
    this.createCalendarStructure();

    // Initialize navigation events
    this.initNavigation();

    // Render the calendar
    this.render();
  }

  createCalendarStructure() {
    // Create header
    const header = document.createElement('div');
    header.className = 'calendar-header';

    const navSection = document.createElement('div');
    navSection.className = 'calendar-nav';

    const prevBtn = document.createElement('button');
    prevBtn.textContent = 'Previous';
    prevBtn.id = 'prev-week';

    const todayBtn = document.createElement('button');
    todayBtn.textContent = 'Today';
    todayBtn.id = 'today';

    const nextBtn = document.createElement('button');
    nextBtn.textContent = 'Next';
    nextBtn.id = 'next-week';

    navSection.appendChild(prevBtn);
    navSection.appendChild(todayBtn);
    navSection.appendChild(nextBtn);

    const title = document.createElement('div');
    title.className = 'calendar-title';
    title.id = 'calendar-title';

    header.appendChild(navSection);
    header.appendChild(title);

    // Create body
    const body = document.createElement('div');
    body.className = 'calendar-body';
    body.id = 'calendar-body';

    // Add to container
    this.container.appendChild(header);
    this.container.appendChild(body);
  }

  initNavigation() {
    document.getElementById('prev-week').addEventListener('click', () => {
      this.prevWeek();
    });

    document.getElementById('today').addEventListener('click', () => {
      this.goToToday();
    });

    document.getElementById('next-week').addEventListener('click', () => {
      this.nextWeek();
    });
  }

  // Calendar navigation
  prevWeek() {
    // Create a new date object to avoid modifying the original directly
    const newDate = new Date(this.currentDate);
    newDate.setDate(newDate.getDate() - 7);
    this.currentDate = newDate;
    this.render();
  }

  nextWeek() {
    // Create a new date object to avoid modifying the original directly
    const newDate = new Date(this.currentDate);
    newDate.setDate(newDate.getDate() + 7);
    this.currentDate = newDate;
    this.render();
  }

  goToToday() {
    // Set to today's date with time set to midnight
    const now = new Date();
    this.currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    this.render();
  }

  // Format date as YYYY-MM-DD
  formatDate(date) {
    // Validate that the date is a valid Date object
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.error('formatDate called with invalid date:', date);
      return 'INVALID-DATE';
    }

    // Ensure consistent date formatting by using local components
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // months are 0-indexed
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Get the start of the week (Sunday)
  getWeekStart(date) {
    // Create a new date object to avoid modifying the original
    // Use local date components to avoid timezone issues
    const d = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const day = d.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const diff = d.getDate() - day; // Calculate days to subtract to get to Sunday
    return new Date(d.setDate(diff));
  }

  // Render calendar week view with time slots
  render() {
    // Define days of week for reference
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Get the current date with time set to midnight to avoid timezone issues
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Get the start of the week (Sunday)
    const weekStart = this.getWeekStart(this.currentDate);

    // Update title
    const endOfWeek = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 6);

    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    const startMonth = monthNames[weekStart.getMonth()];
    const endMonth = monthNames[endOfWeek.getMonth()];

    // Format the title with the correct date range
    let titleText;
    if (startMonth === endMonth) {
      titleText = `${startMonth} ${weekStart.getDate()} - ${endOfWeek.getDate()}, ${weekStart.getFullYear()}`;
    } else {
      titleText = `${startMonth} ${weekStart.getDate()} - ${endMonth} ${endOfWeek.getDate()}, ${weekStart.getFullYear()}`;
    }

    // Update the title text
    document.getElementById('calendar-title').textContent = titleText;

    // Store the week start date for consistent reference throughout rendering
    this._renderWeekStart = weekStart;
    this._renderToday = today;

    // Clear calendar body
    const calendarBody = document.getElementById('calendar-body');
    calendarBody.innerHTML = '';

    // Create time grid structure
    const timeGridContainer = document.createElement('div');
    timeGridContainer.className = 'time-grid-container';

    // Add time axis column (left side with hours)
    const timeAxis = document.createElement('div');
    timeAxis.className = 'time-axis';

    // Add full-day events row at the top
    const fullDayRow = document.createElement('div');
    fullDayRow.className = 'full-day-row';
    fullDayRow.id = 'full-day-events';

    // Add empty cell for the time axis
    const fullDayCorner = document.createElement('div');
    fullDayCorner.className = 'full-day-corner';
    fullDayCorner.textContent = 'All Day';
    fullDayRow.appendChild(fullDayCorner);

    // Create full-day cells for each day
    const fullDayCells = [];
    daysOfWeek.forEach((day, index) => {
      const fullDayCell = document.createElement('div');
      fullDayCell.className = 'full-day-cell';
      fullDayCell.setAttribute('data-date', this.formatDate(
        new Date(this._renderWeekStart.getFullYear(),
                this._renderWeekStart.getMonth(),
                this._renderWeekStart.getDate() + index)
      ));
      fullDayCell.setAttribute('data-day-index', index);
      fullDayCell.setAttribute('data-full-day', 'true');
      // Set explicit grid column to ensure consistent width
      fullDayCell.style.gridColumn = (index + 2).toString(); // +2 because first column is for the corner
      fullDayRow.appendChild(fullDayCell);
      fullDayCells.push(fullDayCell);
    });

    // Add column headers row with days of week
    const headerRow = document.createElement('div');
    headerRow.className = 'day-header-row';

    // Add empty cell for the top-left corner
    const cornerCell = document.createElement('div');
    cornerCell.className = 'corner-cell';
    headerRow.appendChild(cornerCell);

    // Add day headers
    const dayColumns = [];

    daysOfWeek.forEach((day, index) => {
      // Create a new date object for each day of the week
      // Use the stored week start date for consistency
      const date = new Date(this._renderWeekStart.getFullYear(), this._renderWeekStart.getMonth(), this._renderWeekStart.getDate() + index);

      // Create day header
      const dayHeader = document.createElement('div');
      dayHeader.className = 'day-header';

      // Highlight today - use the stored today value for consistency
      if (date.getDate() === this._renderToday.getDate() &&
          date.getMonth() === this._renderToday.getMonth() &&
          date.getFullYear() === this._renderToday.getFullYear()) {
        dayHeader.classList.add('today');
      }

      // Add day name and date
      dayHeader.innerHTML = `${day}<br><span class="day-date">${date.getDate()}</span>`;
      headerRow.appendChild(dayHeader);

      // Create column for this day
      const dayColumn = document.createElement('div');
      dayColumn.className = 'day-column';
      dayColumn.setAttribute('data-date', this.formatDate(date));

      if (dayHeader.classList.contains('today')) {
        dayColumn.classList.add('today');
      }

      dayColumns.push(dayColumn);
    });

    // Add header row to container
    timeGridContainer.appendChild(headerRow);

    // Create grid for the hours (full 24 hours)
    const startHour = 0;
    const endHour = 23;

    // Now add all time slots
    for (let hour = startHour; hour <= endHour; hour++) {
      // Create a row for the full hour that will span both time slots
      const hourRow = document.createElement('div');
      hourRow.className = 'time-row full-hour-row';

      // Create the hour label that will span both 30-min slots
      const hourLabel = document.createElement('div');
      hourLabel.className = 'time-label hour-span';
      const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
      const ampm = hour < 12 ? 'AM' : 'PM';
      hourLabel.textContent = `${formattedHour}:00 ${ampm}`;

      // Add hour label to the first row
      hourRow.appendChild(hourLabel);

      // Add a cell for each day of the week in the full hour row
      dayColumns.forEach(dayColumn => {
        const timeCell = document.createElement('div');
        timeCell.className = 'time-cell';

        // Get the date from the day column
        const dateStr = dayColumn.getAttribute('data-date');
        if (!dateStr) {
          console.error('Missing data-date attribute on day column', dayColumn);
          return;
        }

        // Calculate exact date and time for this cell
        // Parse the YYYY-MM-DD format date string
        const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10));
        // Create date with correct values (month is 0-indexed in JavaScript)
        const cellDate = new Date(year, month - 1, day, hour, 0, 0, 0);

        // Store both the time and the original date for reference
        timeCell.setAttribute('data-time', cellDate.toISOString());
        timeCell.setAttribute('data-date', dateStr);

        // Highlight current time using the stored today value for consistency
        if (cellDate.getDate() === this._renderToday.getDate() &&
            cellDate.getMonth() === this._renderToday.getMonth() &&
            cellDate.getFullYear() === this._renderToday.getFullYear() &&
            cellDate.getHours() === new Date().getHours() &&
            new Date().getMinutes() < 30) {
          timeCell.classList.add('current-time');
        }

        hourRow.appendChild(timeCell);
      });

      // Add the full hour row to the grid
      timeGridContainer.appendChild(hourRow);

      // Create the half-hour row with the same structure as the full-hour row
      const halfHourRow = document.createElement('div');
      halfHourRow.className = 'time-row half-hour-row';

      // Add time label placeholder with the same structure as the hour label
      const placeholderLabel = document.createElement('div');
      placeholderLabel.className = 'time-label-placeholder';
      halfHourRow.appendChild(placeholderLabel);

      // For each day column, create exactly one cell
      for (let i = 0; i < 7; i++) {
        // Get the corresponding day column
        const dayColumn = dayColumns[i];
        if (!dayColumn) {
          console.error('Missing day column at index', i);
          continue;
        }

        const timeCell = document.createElement('div');
        timeCell.className = 'time-cell';

        // Get date from the day column
        const dateStr = dayColumn.getAttribute('data-date');
        if (!dateStr) {
          console.error('Missing data-date attribute on day column', i);
          continue;
        }

        // Calculate exact date and time for the half-hour cell
        const cellDate = new Date(dateStr);
        cellDate.setHours(hour, 30, 0, 0);

        // Store data attributes
        timeCell.setAttribute('data-time', cellDate.toISOString());
        timeCell.setAttribute('data-date', dateStr);
        timeCell.setAttribute('data-day-index', i); // Store the day index for debugging

        // Highlight current time
        const now = new Date();
        if (cellDate.getDate() === now.getDate() &&
            cellDate.getMonth() === now.getMonth() &&
            cellDate.getFullYear() === now.getFullYear() &&
            cellDate.getHours() === now.getHours() &&
            now.getMinutes() >= 30) {
          timeCell.classList.add('current-time');
        }

        halfHourRow.appendChild(timeCell);
      }

      // Add the half-hour row to the grid
      timeGridContainer.appendChild(halfHourRow);
    }

    // Add the time grid to the calendar body
    calendarBody.appendChild(timeGridContainer);

    // Add full-day row after the header row but before the time grid
    timeGridContainer.insertBefore(fullDayRow, timeGridContainer.firstChild);

    // Add events to the calendar
    this.renderEvents();

    // Setup drop targets
    this.setupFullDayDropTargets();
    this.setupTimeSlotDropTargets();
  }

  // Calculate event duration in 30-minute increments
  calculateDurationInSlots(startTime, endTime) {
    if (!endTime) return 1; // Default to 1 slot if no end time
    const diffMs = new Date(endTime) - new Date(startTime);
    const diffMinutes = Math.round(diffMs / 60000);
    return Math.max(1, Math.round(diffMinutes / 30)); // At least 1 slot (30 min)
  }


  // Handle event resizing
  setupEventResizing(eventEl, event, cell) {
    // Create resize handle if it doesn't exist
    let resizeHandle = eventEl.querySelector('.resize-handle');
    if (!resizeHandle) {
      resizeHandle = document.createElement('div');
      resizeHandle.className = 'resize-handle';
      eventEl.appendChild(resizeHandle);
    }

    let startY, startHeight, startTime, initialDuration;
    const gridHeight = cell.offsetHeight; // Height of one time slot (30 minutes)
    const MIN_HEIGHT = gridHeight; // Minimum height (30 minutes)

    const onMouseMove = (e) => {
      if (startY === undefined) return;

      e.preventDefault();
      e.stopPropagation();

      const dy = e.clientY - startY;
      const slotHeight = gridHeight;
      const slotsToAdd = Math.round(dy / slotHeight);
      const newDuration = Math.max(1, initialDuration + slotsToAdd);

      // For all events, use absolute pixel height for consistency
      const heightPx = newDuration * slotHeight - 2; // Account for 2px total margin
      eventEl.style.height = `${heightPx}px`;
      eventEl.style.margin = '1px';
      eventEl.style.boxSizing = 'border-box';

      // Ensure element is over the grid with proper z-index
      eventEl.style.zIndex = '10';

      // Update the data-duration attribute
      eventEl.setAttribute('data-duration', newDuration);

      // Add visual feedback
      eventEl.classList.add('resizing');

      // If we're crossing into other cells, make sure they're highlighted
      if (newDuration > 1) {
        // Apply a temporary background to ensure visibility across cells
        eventEl.style.boxShadow = '0 0 0 1px rgba(255,255,255,0.3) inset';
      }
    };

    const onMouseUp = (e) => {
      if (startY === undefined) return;

      e.stopPropagation();

      const dy = e.clientY - startY;
      const slotHeight = gridHeight;
      const slotsToAdd = Math.round(dy / slotHeight);
      const newDuration = Math.max(1, initialDuration + slotsToAdd);

      // Calculate new end time (30 minutes per slot)
      const newEndTime = new Date(startTime);
      newEndTime.setMinutes(newEndTime.getMinutes() + (newDuration * 30));

      // Find the event in our data model
      const eventIndex = this.events.findIndex(evt => evt.id === event.id);
      if (eventIndex !== -1) {
        // Create a copy of the event with updated end time
        const updatedEvent = {
          ...this.events[eventIndex],
          endTime: newEndTime.toISOString(),
          // Store the duration for future reference when rendering
          duration: newDuration
        };

        // Update the event in our data model
        this.events[eventIndex] = updatedEvent;

        // Notify parent component of the update
        if (this.options.onEventUpdate) {
          this.options.onEventUpdate(updatedEvent);
        }
      }

      // Clean up temporary styles
      eventEl.style.boxShadow = '';
      eventEl.classList.remove('resizing');

      // Remove event listeners
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);

      // Update the data-duration attribute for future reference
      eventEl.setAttribute('data-duration', newDuration);

      // Re-render to ensure proper layout across cells
      this.render();

      // Reset tracking variables
      startY = undefined;
    };

    const onMouseDown = (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Only proceed if clicking on the resize handle
      if (!e.target.classList.contains('resize-handle')) return;

      startY = e.clientY;
      startHeight = eventEl.offsetHeight;
      startTime = new Date(event.date);

      // Calculate initial duration in 30-minute slots
      initialDuration = this.calculateDurationInSlots(
        event.date,
        event.endTime || new Date(new Date(event.date).getTime() + 30 * 60000)
      );

      // Add global event listeners
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp, { once: true });
    };

    // Add mousedown listener to the resize handle
    resizeHandle.addEventListener('mousedown', onMouseDown);

    // Prevent text selection when dragging the handle
    resizeHandle.addEventListener('selectstart', (e) => {
      e.preventDefault();
      return false;
    });
  }

  // Render events on the calendar
  renderEvents() {
    console.log('=== Calendar renderEvents called ===');
    console.log('- Regular events count:', this.events.length);
    console.log('- Google events count:', this.googleEvents.length);
    console.log('- Regular events:', this.events);
    console.log('- Google events:', this.googleEvents);

    // Validate Google events before rendering
    if (this.googleEvents.length > 0) {
      console.log('🔍 Validating Google events for rendering:');
      this.googleEvents.forEach((event, index) => {
        const isValid = event && event.title && event.date;
        console.log(`  Google Event ${index + 1}: ${isValid ? '✅ Valid' : '❌ Invalid'}`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: event.date instanceof Date,
          isFullDay: event.isFullDay,
          isGoogleEvent: event.isGoogleEvent
        });
      });
    }

    // Clear any existing full-day events
    document.querySelectorAll('.full-day-event, .google-full-day-event').forEach(el => el.remove());

    // Get all time cells
    const cells = document.querySelectorAll('.time-cell');

    // Clear any existing time-based events
    document.querySelectorAll('.time-event, .google-time-event').forEach(el => el.remove());

    // Double-check: clear events from each cell specifically
    cells.forEach(cell => {
      const existingEvents = cell.querySelectorAll('.time-event, .google-time-event');
      existingEvents.forEach(el => el.remove());
    });

    // First pass: render full-day events
    const fullDayEventsByDate = {};

    // Group CalenTask full-day events by date
    this.events.forEach(event => {
      if (event.isFullDay) {
        console.log('Processing full-day event:', event);
        const eventDate = new Date(event.date);
        // Format date for logging
        console.log('Event date:', eventDate);

        // Get date string in YYYY-MM-DD format for lookup
        const year = eventDate.getFullYear();
        const month = String(eventDate.getMonth() + 1).padStart(2, '0');
        const day = String(eventDate.getDate()).padStart(2, '0');
        const dateStr = `${year}-${month}-${day}`;

        console.log('Formatted date string:', dateStr);

        if (!fullDayEventsByDate[dateStr]) {
          fullDayEventsByDate[dateStr] = [];
        }
        fullDayEventsByDate[dateStr].push(event);
      }
    });

    console.log('Full day events by date:', fullDayEventsByDate);

    // Render full-day events for each date
    Object.entries(fullDayEventsByDate).forEach(([dateStr, events]) => {
      console.log(`Finding cell for date: ${dateStr} with ${events.length} events`);

      // Get all full-day cells
      const fullDayCells = document.querySelectorAll('.full-day-cell');
      console.log('Full day cells found:', fullDayCells.length);

      // Debug all cell dates
      fullDayCells.forEach(cell => {
        console.log('Cell date attribute:', cell.getAttribute('data-date'));
      });

      // Try to find the matching cell
      const fullDayCell = document.querySelector(`.full-day-cell[data-date="${dateStr}"]`);

      if (!fullDayCell) {
        console.warn(`No full-day cell found for date ${dateStr}`);
        return;
      }

      console.log('Found full-day cell:', fullDayCell);

      // Create container for full-day events
      const eventsContainer = document.createElement('div');
      eventsContainer.className = 'full-day-events-container';

      console.log(`Creating ${events.length} events for cell on date ${dateStr}`);

      // Add each full-day event to the container
      events.forEach(event => {
        console.log('Creating full-day event element for:', event);

        // Create full-day event element
        const eventEl = document.createElement('div');
        eventEl.className = `full-day-event ${event.archived ? 'archived' : ''}`;
        eventEl.setAttribute('draggable', 'true');
        eventEl.setAttribute('data-id', event.id);

        // Set title
        eventEl.innerHTML = `
          <span class="event-title">${event.title}</span>
          <span class="event-time">All day</span>
          <button class="delete-btn" title="Remove from calendar">×</button>
        `;

        console.log('Created event element:', eventEl);

        // Add drag and drop handlers
        eventEl.addEventListener('dragstart', (e) => {
          e.stopPropagation();
          const eventData = JSON.stringify({
            taskId: event.id,
            type: 'full-day-event',
            isFullDay: true
          });
          e.dataTransfer.setData('application/json', eventData);
          e.dataTransfer.setData('text/plain', event.title);
          e.dataTransfer.effectAllowed = 'move';
          eventEl.classList.add('dragging');
        });

        eventEl.addEventListener('dragend', (e) => {
          e.stopPropagation();
          eventEl.classList.remove('dragging');
          document.querySelectorAll('.drag-over').forEach(el => {
            el.classList.remove('drag-over');
          });
        });

        // Add click handler for the delete button
        const deleteBtn = eventEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          if (this.options.onEventRemove) {
            this.options.onEventRemove(event.id);
          }
        });

        // Add click handler for the event itself
        eventEl.addEventListener('click', (e) => {
          if (e.target === deleteBtn) return; // Don't trigger if clicking delete button
          if (this.options.onEventClick) {
            this.options.onEventClick(event);
          }
        });

        // Add to container
        eventsContainer.appendChild(eventEl);
      });

      console.log('Events container created with children:', eventsContainer.children.length);

      // Add the events container to the full-day cell
      fullDayCell.appendChild(eventsContainer);
      console.log('Added events container to full-day cell');
    });

    // Group Google Calendar full-day events by date
    console.log('🔍 Processing Google Calendar full-day events...');
    let googleFullDayCount = 0;
    this.googleEvents.forEach((event, index) => {
      console.log(`  Checking Google event ${index + 1}:`, {
        title: event.title,
        isFullDay: event.isFullDay,
        date: event.date,
        dateType: typeof event.date
      });

      if (event.isFullDay) {
        googleFullDayCount++;
        console.log(`  ✅ Processing Google full-day event ${googleFullDayCount}:`, event);

        try {
          // Validate the event date first
          let eventDate;
          if (event.date instanceof Date) {
            eventDate = event.date;
          } else if (typeof event.date === 'string') {
            eventDate = new Date(event.date);
          } else {
            console.error(`    ❌ Invalid date format for full-day event ${event.title}:`, event.date);
            return; // Skip this event
          }

          console.log(`    Event date object:`, eventDate);
          const isValidDate = !isNaN(eventDate.getTime());
          console.log(`    Event date valid:`, isValidDate);

          if (!isValidDate) {
            console.error(`    ❌ Invalid date value for full-day event ${event.title}:`, eventDate);
            return; // Skip this event
          }

          const dateStr = this.formatDate(eventDate);
          if (dateStr === 'INVALID-DATE') {
            console.error(`    ❌ formatDate returned INVALID-DATE for full-day event ${event.title}`);
            return; // Skip this event
          }

          console.log(`    Date string: ${dateStr}`);

          if (!fullDayEventsByDate[dateStr]) {
            fullDayEventsByDate[dateStr] = [];
          }
          fullDayEventsByDate[dateStr].push({...event, isGoogleEvent: true});
          console.log(`    ✅ Added to fullDayEventsByDate[${dateStr}]`);
        } catch (error) {
          console.error(`    ❌ Error processing Google full-day event:`, error);
        }
      }
    });
    console.log(`📊 Total Google full-day events processed: ${googleFullDayCount}`);

    // Render Google Calendar full-day events for each date
    Object.entries(fullDayEventsByDate).forEach(([dateStr, events]) => {
      const googleEvents = events.filter(e => e.isGoogleEvent);
      if (googleEvents.length === 0) return;

      const fullDayCell = document.querySelector(`.full-day-cell[data-date="${dateStr}"]`);
      if (!fullDayCell) return;

      // Find or create events container
      let eventsContainer = fullDayCell.querySelector('.full-day-events-container');
      if (!eventsContainer) {
        eventsContainer = document.createElement('div');
        eventsContainer.className = 'full-day-events-container';
        fullDayCell.appendChild(eventsContainer);
      }

      // Add Google Calendar events
      googleEvents.forEach(event => {
        const eventEl = document.createElement('div');
        eventEl.className = 'google-full-day-event';
        eventEl.innerHTML = `
          <span class="event-title">${event.title}</span>
          <span class="event-time">All day</span>
          <span class="google-event-indicator">📅</span>
        `;

        // Add click handler for Google events (read-only)
        eventEl.addEventListener('click', (e) => {
          e.stopPropagation();
          if (this.options.onGoogleEventClick) {
            this.options.onGoogleEventClick(event);
          }
        });

        eventsContainer.appendChild(eventEl);
      });
    });

    // Second pass: render time-based events
    this.events.forEach(event => {
      if (event.isFullDay) return; // Skip full-day events in this pass

      const eventDate = new Date(event.date);
      const dateStr = this.formatDate(eventDate);
      const hour = eventDate.getHours();
      const minutes = eventDate.getMinutes();

      // Find the closest time slot (round to nearest 30 min)
      const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
      const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);

      // Find the matching cell
      for (const cell of cells) {
        const cellTime = new Date(cell.getAttribute('data-time'));
        const cellDateStr = this.formatDate(cellTime);

        if (cellDateStr === dateStr &&
            cellTime.getHours() === adjustedHour &&
            cellTime.getMinutes() === roundedMinutes) {

          // Create event element
          const eventEl = document.createElement('div');
          eventEl.classList.add('time-event');
          if (event.archived) {
            eventEl.classList.add('archived');
          }

          // Calculate duration in 30-minute slots
          const duration = this.calculateDurationInSlots(
            event.date,
            event.endTime || new Date(new Date(event.date).getTime() + 30 * 60000)
          );

          // Format start and end times for display
          const startTimeStr = this.formatTime(new Date(event.date));
          const endTimeDate = event.endTime || new Date(new Date(event.date).getTime() + 30 * 60000);
          const endTimeStr = this.formatTime(new Date(endTimeDate));
          const timeRangeStr = `${startTimeStr} - ${endTimeStr}`;

          // Clear any existing content
          eventEl.innerHTML = '';

          // Set event styling
          eventEl.style.backgroundColor = event.archived ? '#28a745' : '#007bff';
          eventEl.style.color = 'white';
          eventEl.style.borderRadius = '3px';
          eventEl.style.cursor = 'grab';
          eventEl.style.position = 'absolute';
          eventEl.style.top = '0';
          eventEl.style.left = '0';
          eventEl.style.right = '0';
          eventEl.style.margin = '1px';
          eventEl.style.zIndex = '5';
          eventEl.style.boxSizing = 'border-box';

          // Use absolute pixel height for all events to ensure consistency
          const slotHeight = cell.offsetHeight;
          // Calculate height considering line weight for multi-row events
          // For single slot events, we subtract 2px for margin
          // For multi-slot events, we need to account for borders between rows
          const borderAdjustment = duration > 1 ? (duration - 1) * 0.5 : 0;
          const heightPx = (duration * slotHeight) - 2 + borderAdjustment;
          eventEl.style.height = `${heightPx}px`;

          // Different display formats based on duration
          if (duration === 1) {
            // Single-slot events (30 minutes): Title and time on same line
            eventEl.style.display = 'flex';
            eventEl.style.flexDirection = 'row';
            eventEl.style.justifyContent = 'space-between';
            eventEl.style.alignItems = 'center';
            eventEl.style.overflow = 'hidden';
            eventEl.style.whiteSpace = 'nowrap';
            eventEl.style.fontSize = '0.7rem';
            eventEl.style.padding = '2px 4px';

            const titleSpan = document.createElement('span');
            titleSpan.textContent = event.title;
            titleSpan.style.overflow = 'hidden';
            titleSpan.style.textOverflow = 'ellipsis';
            titleSpan.style.marginRight = '4px';
            titleSpan.style.flexGrow = '1';
            titleSpan.style.flexShrink = '1';

            const separatorSpan = document.createElement('span');
            separatorSpan.textContent = ' | ';
            separatorSpan.style.flexShrink = '0';

            const timeSpan = document.createElement('span');
            timeSpan.textContent = startTimeStr; // Only show start time for single-slot events
            timeSpan.style.flexShrink = '0';
            timeSpan.style.fontSize = '0.65rem';

            eventEl.appendChild(titleSpan);
            eventEl.appendChild(separatorSpan);
            eventEl.appendChild(timeSpan);
          } else {
            // Multi-slot events: Title on first line, time on second line
            eventEl.style.display = 'flex';
            eventEl.style.flexDirection = 'column';
            eventEl.style.justifyContent = 'flex-start';
            eventEl.style.overflow = 'hidden';
            eventEl.style.fontSize = '0.75rem';
            eventEl.style.padding = '3px 4px';

            const titleDiv = document.createElement('div');
            titleDiv.textContent = event.title;
            titleDiv.style.fontWeight = 'bold';
            titleDiv.style.overflow = 'hidden';
            titleDiv.style.textOverflow = 'ellipsis';
            titleDiv.style.whiteSpace = 'nowrap';

            const timeDiv = document.createElement('div');
            timeDiv.textContent = timeRangeStr;
            timeDiv.style.fontSize = '0.7rem';
            timeDiv.style.marginTop = '2px';
            timeDiv.style.overflow = 'hidden';
            timeDiv.style.textOverflow = 'ellipsis';
            timeDiv.style.whiteSpace = 'nowrap';

            eventEl.appendChild(titleDiv);
            eventEl.appendChild(timeDiv);
          }

          // Add data attribute for duration to help with resizing
          eventEl.setAttribute('data-duration', duration);
          eventEl.setAttribute('title', `${event.title} (${timeRangeStr})`);
          eventEl.setAttribute('draggable', 'true'); // Make the event draggable
          eventEl.setAttribute('data-id', event.id);

          // Add resize handle for resizing
          const resizeHandle = document.createElement('div');
          resizeHandle.className = 'resize-handle';
          eventEl.appendChild(resizeHandle);

          // Drag and drop for rescheduling or unscheduling
          eventEl.addEventListener('dragstart', (e) => {
            e.stopPropagation(); // Prevent task list drag handler interference

            // Store data about this event for drop handling
            const eventData = JSON.stringify({
              taskId: event.id,
              type: 'calendar-event' // Differentiate from task list item
            });

            e.dataTransfer.setData('application/json', eventData);
            e.dataTransfer.setData('text/plain', event.title); // Fallback for some browsers
            e.dataTransfer.effectAllowed = 'move';

            // Visual feedback
            eventEl.classList.add('dragging-event');

            console.log('Calendar event drag started:', event.id);
          });

          eventEl.addEventListener('dragend', (e) => {
            e.stopPropagation();
            eventEl.classList.remove('dragging-event');

            // Clear all drop indicators throughout the UI
            document.querySelectorAll('.drag-target, .calendar-drop-target, .drag-over').forEach(el => {
              el.classList.remove('drag-target');
              el.classList.remove('calendar-drop-target');
              el.classList.remove('drag-over');

              });

              console.log('Calendar event drag ended, indicators cleared');
            });

            // Add resize handle and setup resizing
            this.setupEventResizing(eventEl, event, cell);

            // Add click handler
            eventEl.addEventListener('click', (e) => {
              e.stopPropagation();
              if (this.options.onEventClick) {
                this.options.onEventClick(event);
              }
            });

            // Prevent text selection during resize and handle mouse events
          eventEl.addEventListener('selectstart', (e) => {
            if (e.target.classList.contains('resize-handle')) {
              e.preventDefault();
              return false;
            }
          });

          // Make sure the resize handle doesn't trigger drag events
          eventEl.querySelector('.resize-handle')?.addEventListener('dragstart', (e) => {
            e.preventDefault();
            e.stopPropagation();
            return false;
          });

          // Add to cell
          cell.style.position = 'relative';
          cell.appendChild(eventEl);
          break;
        }
      }
    });

    // Third pass: render Google Calendar time-based events
    console.log('🔍 Processing Google Calendar time-based events...');
    let googleTimeEventCount = 0;
    this.googleEvents.forEach((event, index) => {
      console.log(`  Checking Google event ${index + 1} for time-based rendering:`, {
        title: event.title,
        isFullDay: event.isFullDay,
        date: event.date,
        dateType: typeof event.date
      });

      if (event.isFullDay) {
        console.log(`    ⏭️ Skipping full-day event: ${event.title}`);
        return; // Skip full-day events in this pass
      }

      googleTimeEventCount++;
      console.log(`  ✅ Processing Google time-based event ${googleTimeEventCount}:`, event);

      try {
        // Validate the event date first
        let eventDate;
        if (event.date instanceof Date) {
          eventDate = event.date;
        } else if (typeof event.date === 'string') {
          eventDate = new Date(event.date);
        } else {
          console.error(`    ❌ Invalid date format for event ${event.title}:`, event.date);
          return; // Skip this event
        }

        console.log(`    Event date object:`, eventDate);
        const isValidDate = !isNaN(eventDate.getTime());
        console.log(`    Event date valid:`, isValidDate);

        if (!isValidDate) {
          console.error(`    ❌ Invalid date value for event ${event.title}:`, eventDate);
          return; // Skip this event
        }

        const dateStr = this.formatDate(eventDate);
        if (dateStr === 'INVALID-DATE') {
          console.error(`    ❌ formatDate returned INVALID-DATE for event ${event.title}`);
          return; // Skip this event
        }

        const hour = eventDate.getHours();
        const minutes = eventDate.getMinutes();
        console.log(`    Date string: ${dateStr}, Hour: ${hour}, Minutes: ${minutes}`);

        // Find the closest time slot (round to nearest 30 min)
        const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
        const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);
        console.log(`    Rounded minutes: ${roundedMinutes}, Adjusted hour: ${adjustedHour}`);

        // Find the matching cell
        console.log(`    🔍 Looking for cell with date: ${dateStr}, hour: ${adjustedHour}, minutes: ${roundedMinutes}`);
        console.log(`    📊 Total cells available: ${cells.length}`);

        let cellFound = false;
        let cellsChecked = 0;

        for (const cell of cells) {
          cellsChecked++;
          const cellTimeAttr = cell.getAttribute('data-time');

          if (!cellTimeAttr) {
            if (cellsChecked <= 5) { // Log first 5 cells without data-time
              console.log(`      Cell ${cellsChecked}: No data-time attribute`);
            }
            continue;
          }

          const cellTime = new Date(cellTimeAttr);
          const cellDateStr = this.formatDate(cellTime);

          if (cellsChecked <= 5) { // Log first 5 cells for debugging
            console.log(`      Cell ${cellsChecked}: date=${cellDateStr}, hour=${cellTime.getHours()}, minutes=${cellTime.getMinutes()}, data-time=${cellTimeAttr}`);
          }

          if (cellDateStr === dateStr &&
              cellTime.getHours() === adjustedHour &&
              cellTime.getMinutes() === roundedMinutes) {

            cellFound = true;
            console.log(`    ✅ Found matching cell! Date: ${cellDateStr}, Hour: ${cellTime.getHours()}, Minutes: ${cellTime.getMinutes()}`);
            console.log(`    📍 Cell element:`, cell);
            console.log(`    📍 Cell data-time:`, cellTimeAttr);

            // Create Google event element
            console.log(`    🔧 Creating DOM element for: ${event.title}`);
            const eventEl = document.createElement('div');
            eventEl.classList.add('google-time-event');
            console.log(`    🔧 DOM element created:`, eventEl);

          // Calculate duration in 30-minute slots
          const duration = this.calculateDurationInSlots(
            event.date,
            event.endTime || new Date(new Date(event.date).getTime() + 30 * 60000)
          );

          // Format start and end times for display
          const startTimeStr = this.formatTime(new Date(event.date));
          const endTimeDate = event.endTime || new Date(new Date(event.date).getTime() + 30 * 60000);
          const endTimeStr = this.formatTime(new Date(endTimeDate));
          const timeRangeStr = `${startTimeStr} - ${endTimeStr}`;

          // Set Google event styling (different from CalenTask events)
          eventEl.style.backgroundColor = '#4285f4'; // Google blue
          eventEl.style.color = 'white';
          eventEl.style.borderRadius = '3px';
          eventEl.style.cursor = 'pointer';
          eventEl.style.position = 'absolute';
          eventEl.style.top = '0';
          eventEl.style.left = '0';
          eventEl.style.right = '0';
          eventEl.style.margin = '1px';
          eventEl.style.zIndex = '4'; // Lower than CalenTask events
          eventEl.style.boxSizing = 'border-box';
          eventEl.style.opacity = '0.8'; // Slightly transparent to distinguish

          // Use absolute pixel height for consistency
          const slotHeight = cell.offsetHeight;
          const borderAdjustment = duration > 1 ? (duration - 1) * 0.5 : 0;
          const heightPx = (duration * slotHeight) - 2 + borderAdjustment;
          eventEl.style.height = `${heightPx}px`;

          // Different display formats based on duration
          if (duration === 1) {
            // Single-slot events: Title and Google indicator
            eventEl.style.display = 'flex';
            eventEl.style.flexDirection = 'row';
            eventEl.style.justifyContent = 'space-between';
            eventEl.style.alignItems = 'center';
            eventEl.style.overflow = 'hidden';
            eventEl.style.whiteSpace = 'nowrap';
            eventEl.style.fontSize = '0.7rem';
            eventEl.style.padding = '2px 4px';

            const titleSpan = document.createElement('span');
            titleSpan.textContent = event.title;
            titleSpan.style.overflow = 'hidden';
            titleSpan.style.textOverflow = 'ellipsis';
            titleSpan.style.marginRight = '4px';
            titleSpan.style.flexGrow = '1';
            titleSpan.style.flexShrink = '1';

            const indicatorSpan = document.createElement('span');
            indicatorSpan.textContent = '📅';
            indicatorSpan.style.flexShrink = '0';
            indicatorSpan.style.fontSize = '0.6rem';

            eventEl.appendChild(titleSpan);
            eventEl.appendChild(indicatorSpan);
          } else {
            // Multi-slot events: Title on first line, time on second line
            eventEl.style.display = 'flex';
            eventEl.style.flexDirection = 'column';
            eventEl.style.justifyContent = 'flex-start';
            eventEl.style.overflow = 'hidden';
            eventEl.style.fontSize = '0.75rem';
            eventEl.style.padding = '3px 4px';

            const titleDiv = document.createElement('div');
            titleDiv.style.display = 'flex';
            titleDiv.style.justifyContent = 'space-between';
            titleDiv.style.alignItems = 'center';

            const titleText = document.createElement('span');
            titleText.textContent = event.title;
            titleText.style.fontWeight = 'bold';
            titleText.style.overflow = 'hidden';
            titleText.style.textOverflow = 'ellipsis';
            titleText.style.whiteSpace = 'nowrap';
            titleText.style.flexGrow = '1';

            const indicator = document.createElement('span');
            indicator.textContent = '📅';
            indicator.style.fontSize = '0.6rem';
            indicator.style.marginLeft = '4px';

            titleDiv.appendChild(titleText);
            titleDiv.appendChild(indicator);

            const timeDiv = document.createElement('div');
            timeDiv.textContent = timeRangeStr;
            timeDiv.style.fontSize = '0.7rem';
            timeDiv.style.marginTop = '2px';
            timeDiv.style.overflow = 'hidden';
            timeDiv.style.textOverflow = 'ellipsis';
            timeDiv.style.whiteSpace = 'nowrap';

            eventEl.appendChild(titleDiv);
            eventEl.appendChild(timeDiv);
          }

          eventEl.setAttribute('title', `${event.title} (${timeRangeStr}) - Google Calendar`);
          eventEl.setAttribute('data-google-event-id', event.googleEventId);

          // Add click handler for Google events (read-only)
          eventEl.addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.options.onGoogleEventClick) {
              this.options.onGoogleEventClick(event);
            }
          });

            // Add to cell
            console.log(`    📍 Setting cell position to relative...`);
            cell.style.position = 'relative';
            console.log(`    📍 Cell position set. Current cell children count: ${cell.children.length}`);

            console.log(`    📍 Appending event element to cell...`);
            cell.appendChild(eventEl);
            console.log(`    📍 Event element appended. New cell children count: ${cell.children.length}`);

            // Verify the element is actually in the DOM
            const elementInDom = cell.contains(eventEl);
            const elementVisible = eventEl.offsetParent !== null;
            console.log(`    🔍 Element in DOM: ${elementInDom}`);
            console.log(`    🔍 Element visible: ${elementVisible}`);
            console.log(`    🔍 Element styles:`, {
              display: eventEl.style.display,
              position: eventEl.style.position,
              height: eventEl.style.height,
              backgroundColor: eventEl.style.backgroundColor,
              zIndex: eventEl.style.zIndex
            });

            console.log(`    ✅ Successfully added Google time event to DOM: ${event.title}`);
            break;
        }
        }

        console.log(`    📊 Cells checked: ${cellsChecked}`);
        if (!cellFound) {
          console.warn(`    ⚠️ No matching cell found for ${event.title} at ${dateStr} ${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')}`);
          console.log(`    🔍 Available cells (first 10):`);
          for (let i = 0; i < Math.min(10, cells.length); i++) {
            const cell = cells[i];
            const cellTimeAttr = cell.getAttribute('data-time');
            if (cellTimeAttr) {
              const cellTime = new Date(cellTimeAttr);
              const cellDateStr = this.formatDate(cellTime);
              console.log(`      Cell ${i + 1}: ${cellDateStr} ${cellTime.getHours()}:${cellTime.getMinutes().toString().padStart(2, '0')}`);
            }
          }
        }

      } catch (error) {
        console.error(`    ❌ Error processing Google time-based event:`, error);
      }
    });
    console.log(`📊 Total Google time-based events processed: ${googleTimeEventCount}`);

    // Final DOM verification
    const finalGoogleTimeEvents = document.querySelectorAll('.google-time-event');
    const finalGoogleFullDayEvents = document.querySelectorAll('.google-full-day-event');
    console.log(`📊 Final DOM state:`);
    console.log(`  - Google time events in DOM: ${finalGoogleTimeEvents.length}`);
    console.log(`  - Google full-day events in DOM: ${finalGoogleFullDayEvents.length}`);
    console.log(`  - Total Google events expected: ${this.googleEvents.length}`);

    if (finalGoogleTimeEvents.length === 0 && googleTimeEventCount > 0) {
      console.error(`❌ CRITICAL: ${googleTimeEventCount} Google time events were processed but 0 are in the DOM!`);
    }

    console.log('=== Calendar renderEvents completed ===');
  }

  // Filter events for a specific date
  getEventsForDate(date) {
    const dateStr = this.formatDate(date);
    return this.events.filter(event => {
      const eventDate = this.formatDate(new Date(event.date));
      return eventDate === dateStr;
    });
  }

  // Get formatted time (12-hour format with AM/PM)
  formatTime(date) {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    const hour12 = hours % 12 || 12;
    return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  }

  // Update events
  setEvents(events) {
    console.log('Calendar setEvents called with:', events);
    this.events = events;
    // Force a re-render to update the UI immediately
    this.render();
  }

  // Set Google Calendar events
  setGoogleEvents(googleEvents) {
    console.log('=== Calendar setGoogleEvents called ===');
    console.log('- Input googleEvents:', googleEvents);
    console.log('- Input event count:', googleEvents ? googleEvents.length : 0);
    console.log('- Previous Google events count:', this.googleEvents.length);

    // Validate and sanitize input events
    if (googleEvents && googleEvents.length > 0) {
      console.log('- Sample input events (first 3):');
      googleEvents.slice(0, 3).forEach((event, index) => {
        const isValidDate = event.date instanceof Date && !isNaN(event.date.getTime());
        const isValidEndTime = !event.endTime || (event.endTime instanceof Date && !isNaN(event.endTime.getTime()));

        console.log(`  Input Event ${index + 1}:`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: isValidDate,
          endTime: event.endTime,
          endTimeType: typeof event.endTime,
          isValidEndTime: isValidEndTime,
          isFullDay: event.isFullDay,
          isGoogleEvent: event.isGoogleEvent,
          status: isValidDate && isValidEndTime ? '✅ Valid' : '❌ Invalid'
        });
      });

      // Filter out invalid events and fix any date format issues
      const validatedEvents = googleEvents.map((event, index) => {
        try {
          // Create a copy to avoid modifying the original
          const validatedEvent = { ...event };

          // Ensure date is a proper Date object
          if (typeof event.date === 'string') {
            console.log(`  Converting string date to Date object for event ${index + 1}: "${event.date}"`);
            validatedEvent.date = new Date(event.date);
          } else if (!(event.date instanceof Date)) {
            console.error(`  Invalid date format for event ${index + 1}:`, event.date);
            return null; // Skip this event
          }

          // Validate the date is not invalid
          if (isNaN(validatedEvent.date.getTime())) {
            console.error(`  Invalid date value for event ${index + 1}:`, validatedEvent.date);
            return null; // Skip this event
          }

          // Ensure endTime is a proper Date object if it exists
          if (event.endTime) {
            if (typeof event.endTime === 'string') {
              console.log(`  Converting string endTime to Date object for event ${index + 1}: "${event.endTime}"`);
              validatedEvent.endTime = new Date(event.endTime);
            } else if (!(event.endTime instanceof Date)) {
              console.warn(`  Invalid endTime format for event ${index + 1}, setting to null:`, event.endTime);
              validatedEvent.endTime = null;
            }

            // Validate endTime if it exists
            if (validatedEvent.endTime && isNaN(validatedEvent.endTime.getTime())) {
              console.warn(`  Invalid endTime value for event ${index + 1}, setting to null:`, validatedEvent.endTime);
              validatedEvent.endTime = null;
            }
          }

          return validatedEvent;
        } catch (error) {
          console.error(`  Error validating event ${index + 1}:`, error, event);
          return null; // Skip this event
        }
      }).filter(event => event !== null); // Remove invalid events

      console.log(`📊 Event validation: ${validatedEvents.length} valid out of ${googleEvents.length} input events`);
      this.googleEvents = validatedEvents;
    } else {
      this.googleEvents = [];
    }

    console.log('- New Google events count after assignment:', this.googleEvents.length);

    // Validate stored events
    if (this.googleEvents.length > 0) {
      console.log('- Sample stored events (first 3):');
      this.googleEvents.slice(0, 3).forEach((event, index) => {
        console.log(`  Stored Event ${index + 1}:`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: event.date instanceof Date,
          isFullDay: event.isFullDay,
          endTime: event.endTime,
          isGoogleEvent: event.isGoogleEvent
        });
      });
    }

    // Force a re-render to update the UI immediately
    console.log('🔄 Triggering calendar re-render for Google events...');
    this.render();
    console.log('✅ Calendar re-render completed for Google events');
    console.log('=== Calendar setGoogleEvents completed ===');
  }

  // Get all events (CalenTask + Google Calendar)
  getAllEvents() {
    return [...this.events, ...this.googleEvents];
  }

  // Setup drop targets for full-day events
  setupFullDayDropTargets() {
    const fullDayCells = document.querySelectorAll('.full-day-cell');

    fullDayCells.forEach(cell => {
      // Prevent default drag behaviors
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        cell.addEventListener(eventName, (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
      });

      // Highlight drop target on drag over
      cell.addEventListener('dragover', (e) => {
        cell.classList.add('drag-over');
      });

      // Remove highlight on drag leave
      cell.addEventListener('dragleave', (e) => {
        cell.classList.remove('drag-over');
      });

      // Handle drop
      cell.addEventListener('drop', (e) => {
        console.log('Drop event occurred on full-day cell:', cell);
        cell.classList.remove('drag-over');

        try {
          // Get data types available in the drop
          const types = e.dataTransfer.types;
          console.log('Data types in drop event:', types);

          // Try to parse the dropped data
          const jsonData = e.dataTransfer.getData('application/json');
          console.log('JSON data from drop:', jsonData);

          const data = JSON.parse(jsonData);
          console.log('Parsed drop data:', data);

          const taskId = data.taskId;
          const isExistingEvent = data.type === 'time-event' || data.type === 'full-day-event';
          console.log(`Task ID: ${taskId}, Is existing event: ${isExistingEvent}`);

          if (taskId) {
            const dateStr = cell.getAttribute('data-date');
            console.log('Target date from cell:', dateStr);

            const [year, month, day] = dateStr.split('-').map(Number);
            const eventDate = new Date(year, month - 1, day, 12, 0, 0, 0);
            console.log('Created event date object:', eventDate);

// Find the event in our data model
const eventIndex = this.events.findIndex(evt => evt.id === taskId);
console.log(`Event index in calendar events: ${eventIndex}`);

if (eventIndex !== -1) {
              // This is an existing event being moved to full-day
              const eventToUpdate = this.events[eventIndex];
              const targetDateISOString = eventDate.toISOString().split('T')[0] + 'T00:00:00.000Z';

              console.log('Converting time-based event to full-day:', eventToUpdate);
              console.log('Target date for full-day:', targetDateISOString);

              const updatedEvent = {
                ...eventToUpdate,
                scheduledStart: targetDateISOString,
                scheduledEnd: null,
                isFullDay: true,
                date: targetDateISOString,
                endTime: null
              };

              console.log('Updated full-day event data to be sent:', updatedEvent);
              this.events[eventIndex] = updatedEvent;

              if (this.options.onEventUpdate) {
                console.log('Calling onEventUpdate for full-day transition with data:', updatedEvent);
                this.options.onEventUpdate(updatedEvent);
              } else {
                console.error('onEventUpdate callback not defined');
              }
              this.render();
            } else if (this.options.onTaskDrop) {
              // This is a new task being dropped from the task list
              console.log('Handling new task drop on full-day cell');
              const dropData = {
                taskId,
                date: eventDate.toISOString().split('T')[0] + 'T00:00:00.000Z',
                isFullDay: true
              };
              console.log('Drop data being sent to callback:', dropData);
              this.options.onTaskDrop(dropData);
            }
          } // Closes: if (taskId)
        } catch (error) {
          console.error('Error handling drop:', error);
        }
      }); // Closes: cell.addEventListener('drop', ...)
    }); // Closes: fullDayCells.forEach(...)
  } // Closes: setupFullDayDropTargets()

  // Setup drop targets for time slots
  setupTimeSlotDropTargets() {
    const timeSlots = document.querySelectorAll('.time-cell');

    timeSlots.forEach(slot => {
      // Prevent default drag behaviors
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        slot.addEventListener(eventName, (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
      });

      // Highlight drop target on drag over
      slot.addEventListener('dragover', (e) => {
        slot.classList.add('drag-over');
      });

      // Remove highlight on drag leave
      slot.addEventListener('dragleave', (e) => {
        slot.classList.remove('drag-over');
      });

      // Handle drop
      slot.addEventListener('drop', (e) => {
        slot.classList.remove('drag-over');

        try {
          // Try to parse the dropped data
          const data = JSON.parse(e.dataTransfer.getData('application/json'));
          const taskId = data.taskId;
          const isExistingEvent = data.type === 'time-event' || data.type === 'full-day-event';

          if (taskId) {
            const timeStr = slot.getAttribute('data-time');
            const eventDate = new Date(timeStr);

            // Find the event in our data model
            const eventIndex = this.events.findIndex(evt => evt.id === taskId);

            if (eventIndex !== -1) {
              // Get the existing event
              const event = this.events[eventIndex];
              const isFullDay = event.isFullDay;
              const eventDuration = event.endTime ?
                (new Date(event.endTime) - new Date(event.date)) :
                60 * 60 * 1000; // Default to 1 hour

              // Create updated event
              const updatedEvent = {
                ...event,
                date: eventDate.toISOString(),
                endTime: new Date(eventDate.getTime() + eventDuration).toISOString(),
                isFullDay: false // Explicitly set to false for time slot drops
              };

              // Update the event in the events array
              this.events[eventIndex] = updatedEvent;

              // Notify parent component of the update
              if (this.options.onEventUpdate) {
                this.options.onEventUpdate(updatedEvent);
              }
            } else if (this.options.onTaskDrop) {
              // This is a new task being dropped from the task list
              this.options.onTaskDrop({
                taskId,
                date: eventDate.toISOString(),
                isFullDay: false
              });
            }
          }
        } catch (error) {
          console.error('Error handling drop:', error);
        }
      });
    });
  }
}

// Export to global scope for access from todo.js
window.SimpleCalendar = SimpleCalendar;
