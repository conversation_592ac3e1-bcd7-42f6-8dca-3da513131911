/**
 * Google Calendar Service for CalenTask Chrome Extension
 * Handles Google Calendar API interactions
 */
class GoogleCalendarService {
  constructor(authService) {
    this.authService = authService;
    this.baseUrl = 'https://www.googleapis.com/calendar/v3';
    this.calendars = [];
    this.events = [];
    this.lastSyncTime = null;
    this.syncInterval = null;
    this.isEnabled = false;

    // Load settings from storage
    this.loadSettings();
  }

  /**
   * Load service settings from Chrome storage
   */
  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'google_calendar_enabled',
        'google_calendar_sync_interval',
        'google_calendar_last_sync',
        'google_calendar_events'
      ]);

      this.isEnabled = result.google_calendar_enabled || false;
      this.lastSyncTime = result.google_calendar_last_sync || null;

      // Deserialize cached events to restore Date objects
      const cachedEvents = result.google_calendar_events || [];
      this.events = this.deserializeEvents(cachedEvents);

      const syncInterval = result.google_calendar_sync_interval || 15; // Default 15 minutes
      this.setupAutoSync(syncInterval);
    } catch (error) {
      console.error('Error loading calendar settings:', error);
    }
  }

  /**
   * Save service settings to Chrome storage
   */
  async saveSettings() {
    try {
      await chrome.storage.local.set({
        google_calendar_enabled: this.isEnabled,
        google_calendar_last_sync: this.lastSyncTime,
        google_calendar_events: this.events
      });
    } catch (error) {
      console.error('Error saving calendar settings:', error);
    }
  }

  /**
   * Enable Google Calendar integration
   */
  async enable() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Must be authenticated to enable Google Calendar');
    }

    this.isEnabled = true;
    await this.saveSettings();
    await this.syncCalendarEvents();
    this.setupAutoSync(15); // Sync every 15 minutes
  }

  /**
   * Disable Google Calendar integration
   */
  async disable() {
    this.isEnabled = false;
    this.events = [];
    this.clearAutoSync();
    await this.saveSettings();
  }

  /**
   * Setup automatic syncing
   */
  setupAutoSync(intervalMinutes = 15) {
    this.clearAutoSync();

    if (this.isEnabled && this.authService.isAuthenticated) {
      this.syncInterval = setInterval(() => {
        this.syncCalendarEvents().catch(error => {
          console.error('Auto-sync failed:', error);
        });
      }, intervalMinutes * 60 * 1000);
    }
  }

  /**
   * Clear automatic syncing
   */
  clearAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Get list of user's calendars
   */
  async getCalendarList() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/users/me/calendarList`
      );

      if (response.ok) {
        const data = await response.json();
        this.calendars = data.items || [];
        return this.calendars;
      } else {
        throw new Error(`Failed to fetch calendars: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar list:', error);
      throw error;
    }
  }

  /**
   * Get events from user's primary calendar
   */
  async getCalendarEvents(calendarId = 'primary', timeMin = null, timeMax = null) {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    // Default to current week if no time range specified
    if (!timeMin) {
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      timeMin = weekStart.toISOString();
    }

    if (!timeMax) {
      const now = new Date();
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 6); // End of week (Saturday)
      weekEnd.setHours(23, 59, 59, 999);
      timeMax = weekEnd.toISOString();
    }

    try {
      const params = new URLSearchParams({
        timeMin: timeMin,
        timeMax: timeMax,
        singleEvents: 'true',
        orderBy: 'startTime',
        maxResults: '250'
      });

      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/calendars/${encodeURIComponent(calendarId)}/events?${params}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.items || [];
      } else {
        throw new Error(`Failed to fetch events: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  /**
   * Sync calendar events from Google Calendar
   */
  async syncCalendarEvents() {
    if (!this.isEnabled || !this.authService.isAuthenticated) {
      return;
    }

    try {
      console.log('Syncing Google Calendar events...');

      // Get events for the current week and next week
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay() - 7); // Start from last week
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 14); // End at next week
      weekEnd.setHours(23, 59, 59, 999);

      const events = await this.getCalendarEvents(
        'primary',
        weekStart.toISOString(),
        weekEnd.toISOString()
      );

      // Transform Google Calendar events to our format
      this.events = events.map(event => this.transformGoogleEvent(event));

      this.lastSyncTime = new Date().toISOString();
      await this.saveSettings();

      console.log(`Synced ${this.events.length} Google Calendar events`);

      // Notify listeners about the sync
      this.notifyEventUpdate();

      return this.events;
    } catch (error) {
      console.error('Failed to sync calendar events:', error);
      throw error;
    }
  }

  /**
   * Transform Google Calendar event to our internal format
   */
  transformGoogleEvent(googleEvent) {
    const isAllDay = !googleEvent.start.dateTime;

    let startDate, endDate;

    if (isAllDay) {
      // All-day events use date instead of dateTime
      startDate = new Date(googleEvent.start.date + 'T00:00:00');
      endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00') : startDate;
    } else {
      startDate = new Date(googleEvent.start.dateTime);
      endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
    }

    return {
      id: `google_${googleEvent.id}`,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      date: startDate,
      endTime: isAllDay ? null : endDate,
      isFullDay: isAllDay,
      isGoogleEvent: true,
      googleEventId: googleEvent.id,
      location: googleEvent.location || '',
      attendees: googleEvent.attendees || [],
      htmlLink: googleEvent.htmlLink || '',
      status: googleEvent.status || 'confirmed',
      created: googleEvent.created ? new Date(googleEvent.created) : null,
      updated: googleEvent.updated ? new Date(googleEvent.updated) : null
    };
  }

  /**
   * Deserialize events from Chrome storage, converting string dates back to Date objects
   */
  deserializeEvents(cachedEvents) {
    if (!Array.isArray(cachedEvents)) {
      console.warn('Cached events is not an array:', cachedEvents);
      return [];
    }

    console.log(`🔄 Deserializing ${cachedEvents.length} cached Google Calendar events...`);

    const deserializedEvents = cachedEvents.map((event, index) => {
      try {
        // Create a copy of the event to avoid modifying the original
        const deserializedEvent = { ...event };

        // Convert date strings back to Date objects
        if (event.date) {
          if (typeof event.date === 'string') {
            deserializedEvent.date = new Date(event.date);
            console.log(`  Event ${index + 1}: Converted date string "${event.date}" to Date object`);

            // Validate the converted date
            if (isNaN(deserializedEvent.date.getTime())) {
              console.error(`  Event ${index + 1}: Invalid date string "${event.date}" resulted in Invalid Date`);
              return null; // Skip this event
            }
          } else if (event.date instanceof Date) {
            // Already a Date object, validate it
            if (isNaN(event.date.getTime())) {
              console.error(`  Event ${index + 1}: Date object is Invalid Date:`, event.date);
              return null; // Skip this event
            }
            deserializedEvent.date = event.date;
          } else {
            console.error(`  Event ${index + 1}: Invalid date format (not string or Date):`, event.date);
            return null; // Skip this event instead of using fallback
          }
        } else {
          console.error(`  Event ${index + 1}: Missing date property`);
          return null; // Skip this event
        }

        // Convert endTime strings back to Date objects
        if (event.endTime) {
          if (typeof event.endTime === 'string') {
            deserializedEvent.endTime = new Date(event.endTime);
            console.log(`  Event ${index + 1}: Converted endTime string "${event.endTime}" to Date object`);

            // Validate the converted endTime
            if (isNaN(deserializedEvent.endTime.getTime())) {
              console.warn(`  Event ${index + 1}: Invalid endTime string "${event.endTime}" resulted in Invalid Date, setting to null`);
              deserializedEvent.endTime = null;
            }
          } else if (event.endTime instanceof Date) {
            // Already a Date object, validate it
            if (isNaN(event.endTime.getTime())) {
              console.warn(`  Event ${index + 1}: endTime Date object is Invalid Date, setting to null:`, event.endTime);
              deserializedEvent.endTime = null;
            } else {
              deserializedEvent.endTime = event.endTime;
            }
          } else {
            console.warn(`  Event ${index + 1}: Invalid endTime format (not string or Date), setting to null:`, event.endTime);
            deserializedEvent.endTime = null;
          }
        }

        // Convert created and updated dates if they exist
        if (event.created && typeof event.created === 'string') {
          deserializedEvent.created = new Date(event.created);
        }
        if (event.updated && typeof event.updated === 'string') {
          deserializedEvent.updated = new Date(event.updated);
        }

        // Final validation of the deserialized event
        if (!deserializedEvent.date || !(deserializedEvent.date instanceof Date) || isNaN(deserializedEvent.date.getTime())) {
          console.error(`  Event ${index + 1}: Final validation failed - invalid date:`, deserializedEvent.date);
          return null; // Skip this event
        }

        // Validate endTime if it exists
        if (deserializedEvent.endTime && (!(deserializedEvent.endTime instanceof Date) || isNaN(deserializedEvent.endTime.getTime()))) {
          console.warn(`  Event ${index + 1}: Final validation - invalid endTime, setting to null:`, deserializedEvent.endTime);
          deserializedEvent.endTime = null;
        }

        // Ensure required properties exist
        if (!deserializedEvent.title) {
          console.warn(`  Event ${index + 1}: Missing title, using fallback`);
          deserializedEvent.title = 'Untitled Event';
        }

        console.log(`  ✅ Event ${index + 1} deserialized successfully:`, {
          title: deserializedEvent.title,
          date: deserializedEvent.date,
          dateType: typeof deserializedEvent.date,
          isValidDate: deserializedEvent.date instanceof Date && !isNaN(deserializedEvent.date.getTime()),
          endTime: deserializedEvent.endTime,
          endTimeValid: !deserializedEvent.endTime || (deserializedEvent.endTime instanceof Date && !isNaN(deserializedEvent.endTime.getTime())),
          isFullDay: deserializedEvent.isFullDay,
          isGoogleEvent: deserializedEvent.isGoogleEvent
        });

        return deserializedEvent;
      } catch (error) {
        console.error(`  ❌ Error deserializing event ${index + 1}:`, error, event);
        return null; // Skip this event
      }
    }).filter(event => event !== null); // Remove any null events

    console.log(`✅ Successfully deserialized ${deserializedEvents.length} out of ${cachedEvents.length} cached events`);
    return deserializedEvents;
  }

  /**
   * Get all synced Google Calendar events
   */
  getEvents() {
    return this.events;
  }

  /**
   * Load cached events and notify listeners (useful for ensuring events are displayed)
   */
  loadCachedEvents() {
    console.log('=== loadCachedEvents called ===');
    console.log('- Cached events count:', this.events ? this.events.length : 0);
    console.log('- Cached events array:', this.events);

    if (this.events && this.events.length > 0) {
      console.log(`✅ Loading ${this.events.length} cached Google Calendar events for display`);

      // Validate that events have proper Date objects
      let validEventCount = 0;
      let invalidEventCount = 0;

      // Log sample cached events with detailed validation
      console.log('- Sample cached events (first 3):');
      this.events.slice(0, 3).forEach((event, index) => {
        const isValidDate = event.date instanceof Date && !isNaN(event.date.getTime());
        const isValidEndTime = !event.endTime || (event.endTime instanceof Date && !isNaN(event.endTime.getTime()));

        if (isValidDate && isValidEndTime) {
          validEventCount++;
        } else {
          invalidEventCount++;
        }

        console.log(`  Cached Event ${index + 1}:`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: isValidDate,
          endTime: event.endTime,
          endTimeType: typeof event.endTime,
          isValidEndTime: isValidEndTime,
          isFullDay: event.isFullDay,
          isGoogleEvent: event.isGoogleEvent,
          status: isValidDate && isValidEndTime ? '✅ Valid' : '❌ Invalid'
        });
      });

      // Count all events for validation summary
      this.events.forEach(event => {
        const isValidDate = event.date instanceof Date && !isNaN(event.date.getTime());
        const isValidEndTime = !event.endTime || (event.endTime instanceof Date && !isNaN(event.endTime.getTime()));

        if (isValidDate && isValidEndTime) {
          validEventCount++;
        } else {
          invalidEventCount++;
        }
      });

      console.log(`📊 Event validation summary: ${validEventCount} valid, ${invalidEventCount} invalid`);

      if (invalidEventCount > 0) {
        console.warn(`⚠️ Found ${invalidEventCount} invalid cached events that may not render properly`);
      }

      console.log('🔔 Notifying listeners about cached events...');
      this.notifyEventUpdate();
      console.log('✅ Cached events notification sent');
      return this.events;
    } else {
      console.log('❌ No cached Google Calendar events available');
      return [];
    }
  }

  /**
   * Get events for a specific date range
   */
  getEventsInRange(startDate, endDate) {
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate >= startDate && eventDate <= endDate;
    });
  }

  /**
   * Check if service is enabled and authenticated
   */
  isReady() {
    return this.isEnabled && this.authService.isAuthenticated;
  }

  /**
   * Get sync status information
   */
  getSyncStatus() {
    return {
      isEnabled: this.isEnabled,
      isAuthenticated: this.authService.isAuthenticated,
      lastSyncTime: this.lastSyncTime,
      eventCount: this.events.length,
      isReady: this.isReady()
    };
  }

  /**
   * Notify listeners about event updates
   */
  notifyEventUpdate() {
    console.log('Dispatching googleCalendarEventsUpdated event with', this.events.length, 'events');

    // Dispatch custom event for calendar updates
    const event = new CustomEvent('googleCalendarEventsUpdated', {
      detail: {
        events: this.events,
        syncTime: this.lastSyncTime
      }
    });

    window.dispatchEvent(event);
    console.log('googleCalendarEventsUpdated event dispatched');
  }

  /**
   * Force a manual sync
   */
  async forcSync() {
    if (!this.isReady()) {
      throw new Error('Google Calendar service not ready');
    }

    return await this.syncCalendarEvents();
  }

  /**
   * Perform automatic sync on page load/refresh
   * This method includes additional checks and error handling for automatic sync
   */
  async autoSyncOnPageLoad() {
    try {
      // Check if we should perform auto-sync
      const syncCheck = this.shouldAutoSync();

      if (!syncCheck.shouldSync) {
        console.log(`Auto-sync skipped: ${syncCheck.message}`);

        // Even if we skip the sync, we should still load cached events
        if (this.events && this.events.length > 0) {
          console.log(`Loading ${this.events.length} cached Google Calendar events (sync skipped due to ${syncCheck.reason})`);

          // Notify listeners about the cached events to ensure UI updates
          this.notifyEventUpdate();

          // Return the cached events with detailed information
          return {
            syncPerformed: false,
            eventsLoaded: true,
            events: this.events,
            source: 'cache',
            skipReason: syncCheck.reason,
            skipMessage: syncCheck.message,
            lastSyncTime: this.lastSyncTime
          };
        } else {
          console.log(`No cached events available (sync skipped due to ${syncCheck.reason})`);
          return {
            syncPerformed: false,
            eventsLoaded: false,
            events: [],
            source: 'none',
            skipReason: syncCheck.reason,
            skipMessage: syncCheck.message
          };
        }
      }

      console.log(`Performing automatic Google Calendar sync on page load (${syncCheck.message})...`);

      // Perform the sync
      const events = await this.syncCalendarEvents();

      console.log(`Auto-sync completed: ${events ? events.length : 0} events synced from API`);
      return {
        syncPerformed: true,
        eventsLoaded: true,
        events: events || [],
        source: 'api',
        syncTime: this.lastSyncTime
      };
    } catch (error) {
      console.error('Auto-sync on page load failed:', error);

      // Even if sync fails, try to load cached events
      if (this.events && this.events.length > 0) {
        console.log(`Loading ${this.events.length} cached events after sync failure`);
        this.notifyEventUpdate();
        return {
          syncPerformed: false,
          eventsLoaded: true,
          events: this.events,
          source: 'cache_fallback',
          error: error.message
        };
      }

      // Don't throw the error for auto-sync, just log it
      // This prevents the page load from being interrupted
      return {
        syncPerformed: false,
        eventsLoaded: false,
        events: [],
        source: 'error',
        error: error.message
      };
    }
  }

  /**
   * Check if automatic sync should be performed
   * Returns an object with sync decision and reason
   */
  shouldAutoSync() {
    // Must be enabled and authenticated
    if (!this.isReady()) {
      return {
        shouldSync: false,
        reason: 'service_not_ready',
        message: 'Google Calendar service not ready (not enabled or not authenticated)'
      };
    }

    // Check if we've synced recently (within last 5 minutes)
    // to avoid excessive syncing on rapid page refreshes
    if (this.lastSyncTime) {
      const lastSync = new Date(this.lastSyncTime);
      const now = new Date();
      const timeDiff = now.getTime() - lastSync.getTime();
      const fiveMinutesInMs = 5 * 60 * 1000;

      if (timeDiff < fiveMinutesInMs) {
        const minutesAgo = Math.round(timeDiff / (60 * 1000));
        return {
          shouldSync: false,
          reason: 'recent_sync',
          message: `Recent sync detected (${minutesAgo} minutes ago, cooldown: 5 minutes)`,
          lastSyncTime: this.lastSyncTime,
          timeSinceLastSync: timeDiff
        };
      }
    }

    return {
      shouldSync: true,
      reason: 'ready',
      message: 'Ready to sync'
    };
  }

  /**
   * Force refresh of cached events by reloading and deserializing them from storage
   * This is useful for fixing any cached events that may have invalid date formats
   */
  async refreshCachedEvents() {
    console.log('🔄 Refreshing cached events from storage...');

    try {
      const result = await chrome.storage.local.get(['google_calendar_events']);
      const cachedEvents = result.google_calendar_events || [];

      console.log(`Found ${cachedEvents.length} events in storage`);

      // Deserialize the events to fix any date format issues
      this.events = this.deserializeEvents(cachedEvents);

      console.log(`✅ Refreshed ${this.events.length} cached events`);

      // Notify listeners about the refreshed events
      this.notifyEventUpdate();

      return this.events;
    } catch (error) {
      console.error('❌ Error refreshing cached events:', error);
      throw error;
    }
  }

  /**
   * Clear corrupted cached events and optionally force a new sync
   * This is useful when cached events are completely corrupted and need to be reset
   */
  async clearCorruptedCache(forceSync = false) {
    console.log('🧹 Clearing corrupted cached events...');

    try {
      // Clear the in-memory events
      this.events = [];

      // Clear the cached events in storage
      await chrome.storage.local.set({
        google_calendar_events: []
      });

      console.log('✅ Corrupted cache cleared');

      // Optionally force a new sync to repopulate with fresh data
      if (forceSync && this.isReady()) {
        console.log('🔄 Force syncing fresh events...');
        await this.syncCalendarEvents();
        console.log('✅ Fresh events synced');
      }

      // Notify listeners about the cleared cache
      this.notifyEventUpdate();

      return this.events;
    } catch (error) {
      console.error('❌ Error clearing corrupted cache:', error);
      throw error;
    }
  }
}

// Export for use in other modules
window.GoogleCalendarService = GoogleCalendarService;
